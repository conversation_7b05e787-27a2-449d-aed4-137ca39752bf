<template>
  <el-dialog
    :title="title"
    class="dialog-scenc"
    width="80%"
    :modal-append-to-body="false"
    @close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    destroy-on-close
    v-model="visible"
  >
    <div class="table-box">
      <div class="filter-container">
        <el-input
          @keyup.enter.native="handleFilter"
          size="samll"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'故障单编号'"
          v-model="listQuery.faultCode"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'站址名称'"
          v-model="listQuery.siteName"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'地市'"
          v-model="listQuery.city"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'区县'"
          v-model="listQuery.county"
        >
        </el-input>
        <el-button
          class="filter-item search-btn"
          style="margin-left: 8px"
          size="mini"
          @click="handleFilter"
          >搜索</el-button
        >
        <el-button
        class="filter-item search-btn"
        style="margin-left: 8px"
        size="mini"
        @click="handleExport"
        :loading="exportLoading"
        >导出</el-button>
      </div>
      <el-table
        ref="mainTable"
        height="510"
        border
        fit
        :data="tableList"
        v-loading="listLoading"
        style="width: 100%"
        :row-class-name="tableRowClassName"
      >
        <!-- <el-table-column label="序号" type="index" width="80px" align="center">
          <template slot-scope="scope">
            <span>{{
              (listQuery.pageNo - 1) * listQuery.pageSize + scope.$index + 1
            }}</span>
          </template>
        </el-table-column> -->
        <el-table-column
          label="故障单编号"
          min-width="150px"
          prop="faultCode"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="工单状态"
          min-width="150px"
          prop="workStatus"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="故障类型"
          min-width="150px"
          prop="faultType"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="派单时间"
          min-width="150px"
          prop="dispatchTime"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="接单时间"
          min-width="150px"
          prop="receivingTime"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警时间"
          min-width="150px"
          prop="alarmTime"
          align="center"
          show-overflow-tooltip
        />
        <!-- <el-table-column
          label="时限"
          min-width="150px"
          prop="timeLimit"
          align="center"
          show-overflow-tooltip
        /> -->
        <el-table-column
          label="回单时间"
          min-width="150px"
          prop="receiptTime"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="故障来源"
          min-width="150px"
          prop="faultSource"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="故障标题"
          min-width="150px"
          prop="faultTitle"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="站址运维ID"
          min-width="150px"
          prop="operationId"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="站址名称"
          min-width="150px"
          prop="siteName"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属省份"
          min-width="150px"
          prop="province"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属地市"
          min-width="150px"
          prop="city"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属区县"
          min-width="150px"
          prop="county"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属乡镇"
          min-width="150px"
          prop="town"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="故障原因"
          min-width="150px"
          prop="faultReason"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="是否催办"
          min-width="120px"
          prop="isUrging"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="回单是否超时"
          min-width="150px"
          prop="isTimeout"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="回单人"
          min-width="120px"
          prop="receiptPeople"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="代维公司"
          min-width="150px"
          prop="dwCompany"
          align="center"
          show-overflow-tooltip
        />
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="handleCurrentChange"
    />
  </el-dialog>
  <report-cfg-table ref="reportCfgTable"></report-cfg-table>
</template>

<script>
import { exportGuaranteeInfo,  getGuaranteeInfoPage } from "@/api/sceneView/index";
import reportCfgTable from "./reportCfgTable";
export default {
  name: "reportTable",
  data() {
    return {
      title: "",
      visible: false,
      total: 0,
      listLoading: false,
      tableList: [],
      listQuery: {
        city: "",              // 地市
        county: "",            // 区县
        dateType: "",          // 数据类型（故障总数、超3小时、超6小时、超8小时）
        endTime: "",           // 结束时间
        faultCode: "",         // 故障单编码
        isAsc: "",             // 排序的方式 (desc 或者 asc)
        operator: "",          // 所属运营商（铁塔、电信、联通、移动）
        orderByColumn: "",     // 排序字段
        pageNum: 1,            // 页码
        pageSize: 20,          // 每页数量
        siteName: "",          // 站址名称
        startTime: "",         // 开始时间
      },
      exportLoading: false
    };
  },
  created() {},
  computed: {},
  components: {
    reportCfgTable
  },
  watch: {},
  methods: {
    async handleExport(){
      this.exportLoading = true;
      try {
        const blob = await exportGuaranteeInfo({
          ...this.listQuery,
          pageSize: this.total
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.setAttribute('download', '故障超时预警统计.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(link.href);
      } catch (error) {
        console.error('下载附件失败:', error);
        this.$message.error('下载附件失败');
      }
      this.exportLoading = false;
    },
    handleOpen() {},
    handleCurrentChange(val) {
      this.listQuery.pageNum = val.page;
      this.listQuery.pageSize = val.limit;
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      let { result } = await getGuaranteeInfoPage(this.listQuery);
      this.total = result.total;
      this.tableList = result.list;
      this.listLoading = false;
    },
    addRowColor(record, index) {
      return index % 2 != 0 ? "rowStyl" : "";
    },
    initForm(data) {
      this.title =  '故障工单统计报表'
      this.listQuery = {
        ...this.listQuery,
        ...data,
        pageNum: 1,
        pageSize: 20,
      }
      this.visible = true;
      this.getList();
    },
    handleFilter() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    close() {
      this.listQuery = {
        pageNum: 1,            // 页码
        pageSize: 20,          // 每页数量
      }
      this.visible = false;
    },
    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 != 0 ? "rowStyle" : "";
    },
    handleClose() {
      this.close();
    },
    handleOpenReportCfgTable() {
      this.$refs.reportCfgTable.initForm();
    }
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  text-align: right;
  padding: 8px 10px;
  background-color: #446f86;
  .filter-item {
    margin-bottom: 0;
  }
}
:deep() .el-input__wrapper {
  background-color: transparent;
  &.is-focus {
    box-shadow: 0 0 0 1px #059ec0;
  }
}
:deep() .el-input__inner {
  background-color: transparent;
  border: 0px solid #1296db;
  color: #82bee9;
}
.search-btn {
  background: #059ec0;
  color: #fff;
  border: 1px solid #059ec0;
  margin-top: -3px;
}
</style>

<style lang="scss">
.dialog-scenc {
  background-color: #065e89;
  opacity: 0.9;
  .el-dialog__header {
    height: 55px;
    line-height: 55px;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #059ec0;
    overflow: hidden;
    padding: 0 20px !important;
    .el-dialog__title {
      color: #fff;
    }
  }
  .el-dialog__body {
    padding: 10px 20px 0 20px;
    .rowStyle {
      background: #084969 !important;
    }
    .pagination-container {
      background: transparent;
      border-left: none;
      border-right: none;
      display: flex;
      justify-content: flex-end;
      .el-pagination__total {
        color: #fff;
      }
      .el-pagination__jump {
        color: #fff;
      }
    }
    .table-box {
      .sticky {
        background-color: rgba(144, 147, 153, 0.5);
        .el-input__inner {
          background: linear-gradient(0deg, #385fb866, #2238690d);
          border: 2px solid #059ec0;
          color: #82bee9;
          margin-bottom: 6px;
        }
        .search-btn {
          background: #059ec0;
          color: #fff;
          border: 1px solid #059ec0;
          margin-top: -3px;
        }
      }
      .el-table--enable-row-hover
        .el-table__body
        tr:hover
        > td.el-table__cell {
        background-color: rgba(133, 210, 249, 0.23922) !important;
      }
      .el-table td.el-table__cell {
        border-bottom: 1px solid #059ec0;
        color: #fff;
        height: 45px;
        font-size: 16px;
      }
      .el-table tr {
        background-color: transparent;
        height: 45px;
      }
      .el-table {
        background-color: transparent;
        &::before {
          height: 0;
        }
      }
      .el-table th.el-table__cell {
        background: #084969;
        color: #d2e7ff;
        font-size: 17px;
        font-weight: 700;
        border-bottom: 1px solid #059ec0;
      }
      .el-table__empty-text {
        color: #fff;
      }
      .el-table--border {
        border: 1px solid #059ec0;
      }
      .el-table--border .el-table__cell {
        border-right: 1px solid #059ec0;
      }
      .el-table--border::after {
        width: 0;
      }
      .el-table__body-wrapper {
        .el-table__body {
          width: 100% !important;
        }
        &::-webkit-scrollbar {
          width: 6px;
          height: 12px;
        }
        &::-webkit-scrollbar-thumb {
          // border-radius: 6px;
          background: rgba(144, 147, 153, 0.5);
          border-radius: 0;
          -webkit-box-shadow: inset 0 0 5px #0003;
        }
        &::-webkit-scrollbar-track {
          background: transparent;
          -webkit-box-shadow: inset 0 0 5px #0003;
          border-radius: 0;
        }
      }
    }
  }
}
</style>
